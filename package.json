{"name": "next-init", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start"}, "dependencies": {"@headlessui/react": "^1.7.18", "@heroicons/react": "^2.1.1", "@next/third-parties": "^14.2.25", "@stripe/stripe-js": "^3.0.7", "@tailwindcss/typography": "^0.5.16", "ahooks": "^3.7.10", "autoprefixer": "^10.4.21", "aws-sdk": "^2.1572.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cobe": "^0.6.4", "date-fns": "^3.3.1", "google-auth-library": "^9.6.3", "lucide-react": "^0.541.0", "magic-ui-react": "^1.0.0", "motion": "^12.23.12", "next": "14.2.25", "next-auth": "^4.24.6", "next-intl": "^3.26.0", "pg": "^8.11.3", "react": "^18", "react-dom": "^18", "react-icons": "^5.0.1", "react-markdown": "^9.0.1", "replicate": "^0.27.1", "stripe": "^14.19.0", "tailwind-merge": "^3.3.1", "tailwindcss": "^3.4.17", "tailwindcss-animate": "^1.0.7", "uuid": "^9.0.1"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "14.2.25", "typescript": "^5"}}