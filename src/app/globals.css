@tailwind base;
@tailwind components;
@tailwind utilities;

/* styles.css */
.div-markdown-color h1,
.div-markdown-color h2,
.div-markdown-color h3,
.div-markdown-color h4,
.div-markdown-color h5,
.div-markdown-color h6 {
    @apply text-gray-50;
}

.div-markdown-color a {
    @apply text-gray-200;
}

.div-markdown-color strong {
    @apply text-gray-50;
}

.custom-textarea:focus {
    border: none !important;
    outline: none !important;
}

.background-div {
    background-image: linear-gradient(to left top, #14171f, #151829, #181831, #201739, #2b133e);
    background-repeat: no-repeat;
    background-size: cover;
}

.background-header {
    background-image: linear-gradient(to left top, #14171f, #151829, #181831, #201739, #2b133e);
    background-repeat: no-repeat;
    background-size: cover;
}

.background-footer {
    background-image: linear-gradient(to left top, #14171f, #151829, #181831, #201739, #2b133e);
    background-repeat: no-repeat;
    background-size: cover;
}

.line-clamp-2 {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
}

/* 定义一个棋盘格背景的样式 */
.checkerboard {
    /* 创建两个线性渐变，45度角，模拟棋盘格效果 */
    background-image: linear-gradient(45deg, #f0f0f0 25%, transparent 25%, transparent 75%, #f0f0f0 75%, #f0f0f0),
    linear-gradient(45deg, #f0f0f0 25%, #ffffff 25%, #ffffff 75%, #f0f0f0 75%, #f0f0f0);
    /* 定义每个方格的大小为20px */
    background-size: 20px 20px;
    /* 偏移其中一个渐变，以产生交错效果 */
    background-position: 0 0, 10px 10px;
}

@layer base {
  :root {
        --background: 0 0% 100%;
        --foreground: 0 0% 3.9%;
        --card: 0 0% 100%;
        --card-foreground: 0 0% 3.9%;
        --popover: 0 0% 100%;
        --popover-foreground: 0 0% 3.9%;
        --primary: 0 0% 9%;
        --primary-foreground: 0 0% 98%;
        --secondary: 0 0% 96.1%;
        --secondary-foreground: 0 0% 9%;
        --muted: 0 0% 96.1%;
        --muted-foreground: 0 0% 45.1%;
        --accent: 0 0% 96.1%;
        --accent-foreground: 0 0% 9%;
        --destructive: 0 84.2% 60.2%;
        --destructive-foreground: 0 0% 98%;
        --border: 0 0% 89.8%;
        --input: 0 0% 89.8%;
        --ring: 0 0% 3.9%;
        --chart-1: 12 76% 61%;
        --chart-2: 173 58% 39%;
        --chart-3: 197 37% 24%;
        --chart-4: 43 74% 66%;
        --chart-5: 27 87% 67%;
        --radius: 0.5rem;
    }
  .dark {
        --background: 0 0% 3.9%;
        --foreground: 0 0% 98%;
        --card: 0 0% 3.9%;
        --card-foreground: 0 0% 98%;
        --popover: 0 0% 3.9%;
        --popover-foreground: 0 0% 98%;
        --primary: 0 0% 98%;
        --primary-foreground: 0 0% 9%;
        --secondary: 0 0% 14.9%;
        --secondary-foreground: 0 0% 98%;
        --muted: 0 0% 14.9%;
        --muted-foreground: 0 0% 63.9%;
        --accent: 0 0% 14.9%;
        --accent-foreground: 0 0% 98%;
        --destructive: 0 62.8% 30.6%;
        --destructive-foreground: 0 0% 98%;
        --border: 0 0% 14.9%;
        --input: 0 0% 14.9%;
        --ring: 0 0% 83.1%;
        --chart-1: 220 70% 50%;
        --chart-2: 160 60% 45%;
        --chart-3: 30 80% 55%;
        --chart-4: 280 65% 60%;
        --chart-5: 340 75% 55%;
    }
  .theme {
        --animate-pulse: pulse var(--duration) ease-out infinite;
        --animate-aurora: aurora 8s ease-in-out infinite alternate;
    }
}

@layer base {
  * {
    @apply border-border;
    }
  body {
    @apply bg-background text-foreground;
    }
}

/* Aurora动画关键帧 - 专为AuroraText组件设计 */
@keyframes aurora {
  0% {
    background-position: 0% 50%;
  }
  25% {
    background-position: 50% 100%;
  }
  50% {
    background-position: 100% 50%;
  }
  75% {
    background-position: 50% 0%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* 确保animate-aurora类可用 */
.animate-aurora {
  animation: aurora 8s ease-in-out infinite alternate;
}

/* PulsatingButton动画关键帧 */
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 var(--pulse-color);
  }
  50% {
    box-shadow: 0 0 0 8px var(--pulse-color);
  }
  100% {
    box-shadow: 0 0 0 0 var(--pulse-color);
  }
}

/* 确保animate-pulse类可用 */
.animate-pulse {
  animation: pulse var(--duration) ease-out infinite;
}